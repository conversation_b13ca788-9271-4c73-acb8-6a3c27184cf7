import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    id: string
    action: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const payload = verifyJWT(token)
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { id, action } = params
    const body = await request.json().catch(() => ({}))
    const { adminNotes } = body

    // Validate report exists
    const report = await prisma.report.findUnique({
      where: { id }
    })

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 })
    }

    let updateData: any = {
      reviewedAt: new Date(),
      reviewedBy: payload.userId
    }

    if (adminNotes) {
      updateData.adminNotes = adminNotes
    }

    // Handle different actions
    switch (action) {
      case 'review':
        updateData.status = 'reviewed'
        break

      case 'resolve':
        updateData.status = 'resolved'
        break

      case 'dismiss':
        updateData.status = 'dismissed'
        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    // Update the report
    const updatedReport = await prisma.report.update({
      where: { id },
      data: updateData,
      include: {
        reporter: {
          select: {
            email: true,
            name: true
          }
        },
        reported: {
          select: {
            email: true,
            name: true
          }
        }
      }
    })

    // If resolving a harassment or inappropriate content report,
    // consider additional actions like warning the user
    if (action === 'resolve' && (report.reason.includes('HARASSMENT') || report.reason.includes('INAPPROPRIATE'))) {
      // You could add logic here to:
      // - Send warning email to reported user
      // - Add strike to user's record
      // - Temporarily suspend user if multiple reports
      console.log(`Resolved serious report: ${report.reason} for user ${report.reportedId}`)
    }

    return NextResponse.json({
      success: true,
      report: {
        id: updatedReport.id,
        status: updatedReport.status,
        reviewedAt: updatedReport.reviewedAt?.toISOString(),
        reviewedBy: updatedReport.reviewedBy,
        adminNotes: updatedReport.adminNotes
      }
    })

  } catch (error) {
    console.error('Report action error:', error)
    return NextResponse.json(
      { error: 'Failed to update report' },
      { status: 500 }
    )
  }
}
