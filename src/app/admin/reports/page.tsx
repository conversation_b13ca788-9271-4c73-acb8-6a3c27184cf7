'use client'

import { useEffect, useState } from 'react'
import { Search, Filter, AlertTriangle, Eye, CheckCircle, XCircle, Clock, Flag, User, MessageCircle } from 'lucide-react'

interface Report {
  id: string
  reporterId: string
  reporterEmail: string
  reportedUserId: string
  reportedUserEmail: string
  reportedUserName?: string
  reason: string
  description: string
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED' | 'DISMISSED'
  createdAt: string
  updatedAt: string
  adminNotes?: string
}

export default function AdminReports() {
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    fetchReports()
  }, [currentPage, searchTerm, filterStatus, filterType])

  const fetchReports = async () => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        status: filterStatus,
        type: filterType
      })

      const response = await fetch(`/api/admin/reports?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setReports(data.reports || [])
        setTotalPages(data.totalPages || 1)
      }
    } catch (error) {
      console.error('Failed to fetch reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReportAction = async (reportId: string, action: string, notes?: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch(`/api/admin/reports/${reportId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminNotes: notes })
      })
      
      if (response.ok) {
        fetchReports() // Refresh the list
        setShowModal(false)
        setSelectedReport(null)
      }
    } catch (error) {
      console.error(`Failed to ${action} report:`, error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'REVIEWED':
        return <Eye className="h-4 w-4 text-blue-500" />
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'DISMISSED':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (status) {
      case 'PENDING':
        return <span className={`${baseClasses} bg-yellow-100 text-yellow-700`}>Pending</span>
      case 'REVIEWED':
        return <span className={`${baseClasses} bg-blue-100 text-blue-700`}>Reviewed</span>
      case 'RESOLVED':
        return <span className={`${baseClasses} bg-green-100 text-green-700`}>Resolved</span>
      case 'DISMISSED':
        return <span className={`${baseClasses} bg-gray-100 text-gray-700`}>Dismissed</span>
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-700`}>Unknown</span>
    }
  }

  const getTypeIcon = (reason: string) => {
    if (!reason) return <Flag className="h-4 w-4 text-gray-400" />
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    }
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return <MessageCircle className="h-4 w-4 text-red-600" />
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return <User className="h-4 w-4 text-orange-500" />
    }
    if (upperReason.includes('SPAM')) {
      return <Flag className="h-4 w-4 text-purple-500" />
    }
    return <Flag className="h-4 w-4 text-gray-400" />
  }

  const formatReportType = (reason: string) => {
    if (!reason) return 'Unknown'
    return reason.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getPriorityColor = (reason: string) => {
    if (!reason) return 'border-l-gray-300 bg-white'
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return 'border-l-red-500 bg-red-50'
    }
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return 'border-l-orange-500 bg-orange-50'
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return 'border-l-yellow-500 bg-yellow-50'
    }
    return 'border-l-gray-300 bg-white'
  }

  const getPriorityBorderColor = (reason: string) => {
    if (!reason) return 'bg-gray-300'
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return 'bg-red-500'
    }
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return 'bg-orange-500'
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return 'bg-yellow-500'
    }
    return 'bg-gray-300'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">User Reports</h1>
            <p className="text-gray-600 mt-1">Review and manage user safety reports</p>
          </div>
        </div>

        {/* Loading Search Bar */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="w-32 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="w-32 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Loading Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
              <div className="h-1 bg-gray-200"></div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-16 bg-gray-200 rounded"></div>
                <div className="space-y-1">
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
                <div className="flex gap-2">
                  <div className="flex-1 h-8 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1 h-8 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Reports</h1>
          <p className="text-gray-600 mt-1">Review and manage user safety reports</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Pending Reports</p>
          <p className="text-2xl font-bold text-red-600">
            {reports.filter(r => r.status === 'PENDING').length}
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by reporter or reported user email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="REVIEWED">Reviewed</option>
              <option value="RESOLVED">Resolved</option>
              <option value="DISMISSED">Dismissed</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="HARASSMENT">Harassment</option>
              <option value="INAPPROPRIATE_CONTENT">Inappropriate Content</option>
              <option value="FAKE_PROFILE">Fake Profile</option>
              <option value="SPAM">Spam</option>
              <option value="OTHER">Other</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {reports.length === 0 ? (
          <div className="col-span-full bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <Flag className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-xl font-medium text-gray-500 mb-2">No reports found</p>
            <p className="text-sm text-gray-400">Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          reports.map((report) => (
            <div
              key={report.id}
              className="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 overflow-hidden"
            >
              {/* Card Header */}
              <div className={`h-1 ${getPriorityBorderColor(report.reason)}`}></div>

              <div className="p-6">
                {/* Status and Type */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(report.reason)}
                    <span className="text-sm font-medium text-gray-700">
                      {formatReportType(report.reason)}
                    </span>
                  </div>
                  {getStatusBadge(report.status)}
                </div>

                {/* Reported User */}
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-600">Reported User</span>
                  </div>
                  <p className="font-semibold text-gray-900 truncate">
                    {report.reportedUserName || 'No name'}
                  </p>
                  <p className="text-sm text-gray-500 truncate">{report.reportedUserEmail}</p>
                </div>

                {/* Description */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-600 mb-2">Description</p>
                  <p className="text-sm text-gray-700 line-clamp-3 bg-gray-50 p-3 rounded-lg">
                    {report.description}
                  </p>
                </div>

                {/* Reporter Info */}
                <div className="mb-4 pb-4 border-b border-gray-100">
                  <p className="text-xs text-gray-500 mb-1">Reported by</p>
                  <p className="text-sm text-gray-600 truncate">{report.reporterEmail}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(report.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Admin Notes */}
                {report.adminNotes && (
                  <div className="mb-4">
                    <p className="text-xs text-gray-500 mb-1">Admin Notes</p>
                    <p className="text-sm text-gray-700 bg-blue-50 p-2 rounded text-xs line-clamp-2">
                      {report.adminNotes}
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2">
                  {report.status === 'PENDING' && (
                    <>
                      <button
                        onClick={() => {
                          setSelectedReport(report)
                          setShowModal(true)
                        }}
                        className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      >
                        Review
                      </button>
                      <button
                        onClick={() => handleReportAction(report.id, 'dismiss')}
                        className="flex-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                      >
                        Dismiss
                      </button>
                    </>
                  )}
                  {report.status === 'REVIEWED' && (
                    <button
                      onClick={() => handleReportAction(report.id, 'resolve')}
                      className="w-full px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                    >
                      Resolve
                    </button>
                  )}
                  {(report.status === 'RESOLVED' || report.status === 'DISMISSED') && (
                    <div className="w-full px-3 py-2 text-sm text-center text-gray-500 bg-gray-50 rounded-lg">
                      {report.status === 'RESOLVED' ? 'Resolved' : 'Dismissed'}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Review Modal */}
      {showModal && selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-100">
              <div className="flex items-center gap-3">
                {getTypeIcon(selectedReport.reason)}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Review Report</h3>
                  <p className="text-sm text-gray-500">{formatReportType(selectedReport.reason)}</p>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="px-6 py-4 space-y-4">
              {/* Report Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Reported User</p>
                    <p className="text-gray-900">{selectedReport.reportedUserName || 'No name'}</p>
                    <p className="text-gray-500 text-xs">{selectedReport.reportedUserEmail}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Reported By</p>
                    <p className="text-gray-500">{selectedReport.reporterEmail}</p>
                    <p className="text-gray-400 text-xs">
                      {new Date(selectedReport.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <p className="font-medium text-gray-700 mb-2">Report Description</p>
                <div className="bg-white border border-gray-200 rounded-lg p-3 text-sm text-gray-700">
                  {selectedReport.description}
                </div>
              </div>

              {/* Admin Notes Input */}
              <div>
                <label htmlFor="adminNotes" className="block font-medium text-gray-700 mb-2">
                  Admin Review Notes
                </label>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={4}
                  placeholder="Enter your review notes and any actions taken..."
                  id="adminNotes"
                />
                <p className="text-xs text-gray-500 mt-1">
                  These notes will be saved with the report for future reference.
                </p>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="px-6 py-4 border-t border-gray-100 flex gap-3">
              <button
                onClick={() => {
                  const notes = (document.getElementById('adminNotes') as HTMLTextAreaElement)?.value
                  handleReportAction(selectedReport.id, 'review', notes)
                }}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Mark as Reviewed
              </button>
              <button
                onClick={() => {
                  setShowModal(false)
                  setSelectedReport(null)
                }}
                className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
